import { ImageStyleTemplate } from "../models/types";

/**
 * Generate an image prompt input based on content and style
 * @param content The content to generate an image for
 * @param style The style to use
 * @param styleDescription The description of the style
 * @param detailedDescription Detailed description of the style
 * @returns The prompt input
 */
export const generateImagePromptInput = (
    content: string,
    style: string,
    styleDescription: string,
    detailedDescription: string
) => {
    return `You are tasked with generating an image prompt for a social media post based on a specified style.
Your goal is to create a detailed and vivid image prompt that captures the essence of the content while being optimized for social media engagement.

You are given the following inputs:
<content>
${content}
</content>

<style>
${style}
</style>

<style_description>
${detailedDescription}
</style_description>

A good social media image prompt in ${styleDescription} style consists of the following elements:
1. Bold, attention-grabbing main subject
2. Clear, uncomplicated composition
3. Style-appropriate visual elements
4. Limited text elements (if any)
5. Visual elements that encourage sharing

To generate the Twitter-optimized image prompt, follow these steps:

1. Analyze the content text carefully, identifying key themes, emotions, and visual elements that would resonate on social media.
...

Construct your image prompt using the following structure:
1. Main subject: Describe the primary focus that will immediately catch attention
2. Composition: Specify a layout that works well in social media formats
3. Colors: Focus on color combinations typical of ${styleDescription} style
4. Lighting: Describe lighting that creates visual interest appropriate to the style
5. Mood: Specify the emotional impact you want to create
6. Style details: Include specific ${styleDescription} style elements that enhance authenticity

Ensure that your prompt creates imagery that is instantly understandable, visually striking, and optimized for social media sharing. LIMIT the image prompt to 150 words or less.

Write a prompt. Only include the prompt and nothing else. If possible try to generate animated PNG.`;
};

/**
 * Detailed style descriptions for image generation
 */
export const styleDescriptions = {
    // Original styles
    photorealistic: `Photorealism aims to create images indistinguishable from photographs with extreme detail and precision in all elements, accurate lighting and shadows, proper perspective, natural textures, depth of field effects, subtle imperfections for authenticity, color accuracy, and natural environmental elements. This style prioritizes technical accuracy over artistic interpretation.`,
    watercolor: `Watercolor style features soft, transparent colors with visible paper texture, fluid color transitions, gentle color bleeding, white space as a design element, soft edges, layered washes, and a delicate, ethereal quality. This style emphasizes lightness and spontaneity with a characteristic luminosity.`,
    pixel_art: `Pixel art features deliberately limited resolution with visible square pixels, restricted color palettes, sharp edges, no anti-aliasing, careful placement of individual pixels, and often uses dithering for gradients. This style embraces digital constraints for a distinctive retro aesthetic.`,
    oil_painting: `Oil painting style features rich, vibrant colors with visible brushstrokes, textured surfaces, depth through layering, subtle color blending, dramatic lighting effects, and a sense of permanence and substance. This style emphasizes the materiality of paint and the artist's hand.`,
    pencil_sketch: `Pencil sketch style features fine, precise lines with varying pressure, hatching and cross-hatching for shading, minimal or no color, emphasis on form and contour, attention to light and shadow, and a raw, immediate quality. This style emphasizes draftsmanship and observation.`,
    cyberpunk: `Cyberpunk style features neon-lit urban dystopias with high-tech elements contrasted against decay, holographic displays, cybernetic modifications, rain-slicked streets reflecting lights, stark color contrasts (especially purples, blues, and pinks), and a gritty, noir atmosphere. This style emphasizes the juxtaposition of advanced technology and social breakdown.`,
    impressionist: `Impressionist style features visible brushstrokes with broken color, emphasis on light and its changing qualities, outdoor settings, vibrant complementary colors, scenes from everyday life, and a sense of spontaneity and movement. This style prioritizes the impression of a moment over detail.`,
    abstract: `Abstract style features non-representational forms with emphasis on color, shape, and line, freedom from recognizable subject matter, emotional expression through formal elements, bold compositions, and often geometric or organic patterns. This style prioritizes visual language over literal representation.`,
    pop_art: `Pop art features bold, simple imagery with vibrant, saturated colors, flat areas of color with strong outlines, repetition and patterns, imagery from popular culture and advertising, and often includes irony or humor. This style embraces commercial techniques and mass culture.`,
    low_poly: `Low poly style features geometric, faceted surfaces with a limited number of polygons, flat or smoothly graded color on each face, simplified forms, clean edges, and a distinctive 3D rendered look. This style embraces digital minimalism through reduced complexity.`,
    isometric: `Isometric style features a specific 30-degree angular perspective with no vanishing point, creating a 3D effect where vertical lines remain vertical and horizontal lines are drawn at 30-degree angles. Characterized by clean geometric shapes, precise edges, consistent scale across the image, and often includes architectural or technical elements. This style emphasizes clarity and technical precision in a three-dimensional space.`,
    ukiyo_e: `Ukiyo-e style features traditional Japanese woodblock print aesthetics with flat color planes, bold outlines, distinctive compositions with asymmetrical balance, detailed patterns, natural themes, and dramatic perspectives. Characterized by rich colors, stylized clouds and waves, careful attention to textile patterns, and often incorporates calligraphic elements. This style emphasizes elegant simplicity with decorative detail.`,

    // Medium styles
    stencil: `Stencil style features bold, minimalist designs created by spraying or rolling paint over cutout shapes. Characterized by flat, overlapping colors, sharp edges, high contrast, and simplified forms. This style emphasizes graphic impact and works best for simple subject matters with strong silhouettes.`,
    papercraft: `Papercraft style features three-dimensional paper constructions with clean folds, layered elements, and geometric precision. Characterized by flat color planes, sharp edges, dimensional depth, and often includes cut-out effects and shadow play. This style emphasizes craftsmanship and structural design.`,
    marker_illustration: `Marker illustration style features vibrant colors applied with colored markers, creating sharp edges and unique shading effects through color blending. Characterized by saturated hues, visible stroke patterns, and the distinctive texture of marker application. This style is commonly seen in fashion design and comic art.`,
    risograph: `Risograph style features unique textures and vibrant colors similar to screen printing, with slightly unpredictable ink coverage creating variations in color density. Characterized by grain texture, limited color palettes, and a distinctive retro aesthetic often used for posters and art prints.`,
    graffiti: `Graffiti style features bold, expressive lettering and imagery with vibrant colors, dynamic compositions, and urban aesthetics. Characterized by spray paint textures, layered elements, strong outlines, and often includes stylized characters and backgrounds.`,
    ink_wash: `Ink wash style features fluid, translucent black ink applied in varying concentrations to create gradations from light to dark. Characterized by organic flow, subtle gradients, minimal color, and emphasis on form and movement through tonal variation.`,
    quilling: `Quilling style features intricate designs created from rolled, shaped, and glued paper strips. Characterized by delicate spiral patterns, dimensional texture, precise geometric forms, and often creates mandala-like or floral compositions with fine detail work.`,
    charcoal: `Charcoal style features rich, deep blacks with soft gradations and smudged textures. Characterized by dramatic contrast, expressive mark-making, atmospheric effects, and the ability to create both fine details and broad tonal areas.`,
    collage: `Collage style features compositions created from various materials, papers, and images assembled together. Characterized by mixed textures, layered elements, contrasting scales, and often includes found imagery combined in unexpected ways.`,
    mosaic: `Mosaic style features images composed of small, distinct pieces of colored material arranged to form patterns or pictures. Characterized by tessellated surfaces, geometric precision, rich color combinations, and the interplay between individual elements and the overall composition.`,

    // Material styles
    porcelain: `Porcelain style features glossy, smooth surfaces with a characteristic white or light-colored ceramic appearance. Characterized by refined elegance, delicate translucency, often with decorative patterns, and a pristine, polished finish that reflects light beautifully.`,
    light: `Light style features subjects rendered as glowing, ethereal objects with luminous properties. Characterized by radiant effects, soft illumination, transparency, and often creates magical or otherworldly atmospheres with bright, luminous qualities.`,
    candy: `Candy style features bright, saturated colors with glossy, sweet-looking surfaces. Characterized by vibrant hues, smooth textures, playful aesthetics, and often includes translucent or crystalline effects that evoke confectionery treats.`,
    bubbles: `Bubble style features translucent, spherical forms with iridescent surfaces and light refraction effects. Characterized by transparency, rainbow reflections, delicate fragility, and floating, weightless qualities.`,
    crystals: `Crystal style features faceted, geometric surfaces with light refraction and prismatic effects. Characterized by angular cuts, brilliant reflections, transparency or translucency, and often displays rainbow spectrum effects through the crystal structure.`,
    ceramic: `Ceramic style features smooth, fired clay surfaces with matte or glazed finishes. Characterized by earthy textures, handcrafted qualities, often with visible throwing marks or glazing effects, and warm, organic aesthetics.`,
    plastic: `Plastic style features smooth, synthetic surfaces with uniform coloring and modern industrial aesthetics. Characterized by clean lines, bright colors, reflective or matte finishes, and contemporary manufactured appearance.`,
    wood: `Wood style features natural grain patterns, organic textures, and warm earth tones. Characterized by visible wood grain, natural imperfections, rich brown hues, and tactile, organic surfaces that convey craftsmanship and natural beauty.`,
    metal: `Metal style features reflective, industrial surfaces with metallic luster and cool tones. Characterized by high reflectivity, sharp edges, industrial precision, and often includes oxidation, patina, or polished finishes.`,
    water: `Water style features fluid, transparent surfaces with flowing movement and light refraction. Characterized by transparency, ripple effects, reflective properties, and dynamic movement that captures the essence of liquid motion.`,
    glass: `Glass style features transparent or translucent surfaces with light refraction and reflection. Characterized by crystal clarity, smooth surfaces, light play, and often includes subtle distortions or prismatic effects.`,
    sand: `Sand style features granular textures with warm, earthy tones and natural, weathered appearance. Characterized by fine particle textures, desert colors, natural erosion patterns, and organic, flowing forms.`,
    rain: `Rain style features water droplets, wet surfaces, and atmospheric moisture effects. Characterized by reflective wet surfaces, droplet patterns, misty atmospheres, and the fresh, clean aesthetic of precipitation.`,

    // Photography styles
    high_key_photograph: `High key photography features bright, well-illuminated subjects with minimal shadows and an optimistic feel. Characterized by overexposed highlights, soft lighting, light tones, and generally cheerful, airy atmospheres.`,
    low_key_photograph: `Low key photography features dramatic shadows and dark tones with moody, mysterious atmospheres. Characterized by underexposed areas, strong contrast, minimal lighting, and often conveys tension or drama.`,
    low_angle_photograph: `Low angle photography captures subjects from below, making them appear imposing and powerful. Characterized by upward perspective, dramatic sky backgrounds, enhanced subject prominence, and often conveys strength or authority.`,
    high_angle_photograph: `High angle photography captures subjects from above, making them appear vulnerable or isolated. Characterized by downward perspective, environmental context, diminished subject scale, and often conveys vulnerability or overview.`,
    extreme_close_up: `Extreme close-up photography fills the frame with small details, often cutting off parts of the subject. Characterized by intimate detail focus, shallow depth of field, texture emphasis, and reveals hidden or overlooked elements.`,
    low_shutter_speed_photograph: `Low shutter speed photography captures motion blur and light trails with the camera's shutter open longer. Characterized by motion streaks, light trails, dynamic movement, and often shows the passage of time.`,
    bokeh_photograph: `Bokeh photography features sharp subjects with beautifully blurred backgrounds. Characterized by shallow depth of field, creamy background blur, subject isolation, and often includes circular light highlights.`,
    silhouette_photograph: `Silhouette photography features dark subject outlines against bright backgrounds. Characterized by strong contrast, shape emphasis, dramatic lighting, and often creates mysterious or artistic effects.`,
    black_and_white_photograph: `Black and white photography removes color to emphasize form, texture, and contrast. Characterized by monochromatic tones, dramatic shadows, texture emphasis, and timeless, classic aesthetics.`,
    birds_eye_view: `Bird's-eye view photography captures subjects from directly above. Characterized by top-down perspective, pattern revelation, spatial relationships, and often creates abstract or geometric compositions.`,
    worms_eye_view: `Worm's-eye view photography captures subjects from ground level looking up. Characterized by extreme low angle, dramatic perspective, sky prominence, and often creates powerful, imposing compositions.`,
    dutch_angle: `Dutch angle photography tilts the camera to create diagonal compositions. Characterized by tilted horizon lines, dynamic tension, unbalanced feeling, and often conveys unease or excitement.`,
    long_exposure_photograph: `Long exposure photography uses extended shutter times to capture movement over time. Characterized by smooth water, light trails, cloud streaks, and ethereal, dreamlike motion effects.`,

    // Lighting styles
    natural_lighting: `Natural lighting uses sunlight or moonlight to illuminate subjects authentically. Characterized by realistic shadows, warm or cool color temperatures, directional light, and creates authentic, believable atmospheres.`,
    light_and_shadow: `Light and shadow style emphasizes dramatic contrasts between illuminated and dark areas. Characterized by strong directional lighting, deep shadows, high contrast, and often creates moody, dramatic compositions.`,
    volumetric_lighting: `Volumetric lighting features visible light beams illuminated by dust, smoke, or fog. Characterized by three-dimensional light rays, atmospheric effects, dramatic illumination, and often creates mystical or cinematic moods.`,
    neon_lighting: `Neon lighting features bright, saturated artificial light with retro-futuristic aesthetics. Characterized by vibrant colors, electric glow, urban nighttime settings, and often conveys modern, edgy, or cyberpunk themes.`,
    golden_hour: `Golden hour lighting captures the warm, soft light just after sunrise or before sunset. Characterized by golden color temperature, long shadows, romantic atmosphere, and flattering, diffused illumination.`,
    blue_hour: `Blue hour lighting captures the deep blue twilight just after sunset or before sunrise. Characterized by cool blue tones, balanced artificial and natural light, serene atmosphere, and often includes city lights.`,
    backlighting: `Backlighting places the light source behind the subject, creating silhouettes or rim lighting. Characterized by subject outlining, lens flare effects, dramatic contrast, and often creates ethereal or mysterious moods.`,
    chiaroscuro: `Chiaroscuro lighting features dramatic contrasts between light and dark areas. Characterized by strong directional light, deep shadows, Renaissance-inspired aesthetics, and creates powerful, artistic compositions.`,
    god_rays: `God rays lighting features dramatic beams of light breaking through clouds or openings. Characterized by visible light shafts, spiritual atmosphere, natural drama, and often conveys divine or transcendent themes.`,
    candlelight: `Candlelight features warm, flickering illumination with intimate, cozy atmospheres. Characterized by warm orange tones, soft shadows, romantic mood, and creates intimate, traditional settings.`,
    street_lighting: `Street lighting features urban artificial illumination with nighttime city aesthetics. Characterized by orange sodium lights, urban atmosphere, night photography, and often conveys metropolitan or noir themes.`,
    softbox_lighting: `Softbox lighting features diffused, even illumination commonly used in studio photography. Characterized by soft shadows, even light distribution, professional quality, and creates clean, commercial aesthetics.`,
    moonlight: `Moonlight features cool, silvery illumination with mysterious nighttime atmospheres. Characterized by blue-white tones, soft shadows, romantic or mysterious mood, and creates ethereal, nocturnal settings.`,
    fairy_lights: `Fairy lights feature small, twinkling illumination with magical, whimsical atmospheres. Characterized by warm point lights, bokeh effects, festive mood, and creates enchanting, celebratory settings.`,

    // Color and palette styles
    cool_tones: `Cool tones feature blues, greens, and purples that evoke calm and tranquility. Characterized by soothing color palettes, peaceful atmospheres, refreshing aesthetics, and often conveys serenity or professionalism.`,
    warm_tones: `Warm tones feature reds, oranges, and yellows that create inviting and comfortable feelings. Characterized by cozy color palettes, energetic atmospheres, welcoming aesthetics, and often conveys comfort or excitement.`,
    pastels: `Pastel colors feature soft, muted tones with high lightness and low saturation. Characterized by gentle color palettes, dreamy atmospheres, delicate aesthetics, and often conveys innocence or romance.`,
    vibrant: `Vibrant colors feature highly saturated, intense hues with maximum color impact. Characterized by bold color palettes, energetic atmospheres, attention-grabbing aesthetics, and often conveys excitement or modernity.`,
    earth_tones: `Earth tones feature colors inspired by nature including browns, tans, and muted greens. Characterized by natural color palettes, grounded atmospheres, organic aesthetics, and often conveys stability or authenticity.`,
    jewel_tones: `Jewel tones feature deeply saturated colors inspired by precious stones. Characterized by rich color palettes, luxurious atmospheres, sophisticated aesthetics, and often conveys elegance or opulence.`,
    monochromatic_blues: `Monochromatic blues feature various shades and tints of blue only. Characterized by unified color schemes, calming atmospheres, cohesive aesthetics, and often conveys depth or tranquility.`,
    earthy_reds_and_oranges: `Earthy reds and oranges feature warm, natural tones inspired by autumn and earth. Characterized by rustic color palettes, cozy atmospheres, natural aesthetics, and often conveys warmth or harvest themes.`,
    neon_graffiti: `Neon graffiti colors feature bright, electric hues with urban street art aesthetics. Characterized by fluorescent color palettes, edgy atmospheres, rebellious aesthetics, and often conveys youth culture or urban energy.`,
    autumn_leaves: `Autumn leaves colors feature the warm palette of fall foliage. Characterized by seasonal color schemes, nostalgic atmospheres, natural beauty, and often conveys change or the passage of time.`,
    deep_sea_blues: `Deep sea blues feature dark, mysterious blue tones inspired by ocean depths. Characterized by profound color palettes, mysterious atmospheres, aquatic aesthetics, and often conveys depth or mystery.`,
    grayscale: `Grayscale features only black, white, and gray tones without color. Characterized by monochromatic schemes, timeless atmospheres, classic aesthetics, and often conveys sophistication or drama.`,
    sepia: `Sepia features warm brown monochromatic tones reminiscent of vintage photography. Characterized by nostalgic color schemes, aged atmospheres, historical aesthetics, and often conveys memory or antiquity.`,
    primary_colors: `Primary colors feature pure red, blue, and yellow in bold combinations. Characterized by fundamental color schemes, bold atmospheres, graphic aesthetics, and often conveys simplicity or childhood themes.`,
    rainbow_spectrum: `Rainbow spectrum features the full range of visible colors in vibrant display. Characterized by complete color palettes, joyful atmospheres, celebratory aesthetics, and often conveys diversity or pride.`,
    metallics: `Metallic colors feature reflective, lustrous tones including gold, silver, and copper. Characterized by luxurious color palettes, sophisticated atmospheres, premium aesthetics, and often conveys wealth or modernity.`,
};

/**
 * Templates for each image style
 */
export const imageStyleTemplates: Record<string, ImageStyleTemplate> = {
    photorealistic: {
        systemPrompt: `You are an expert in writing prompts for photorealistic image generation. You excel at creating lifelike, highly detailed visual descriptions that work well in social media feeds. Focus on realistic lighting, textures, and compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "photorealistic",
                "photorealistic",
                styleDescriptions.photorealistic
            ),
    },

    watercolor: {
        systemPrompt: `You are an expert in writing prompts for watercolor-style image generation. You excel at creating soft, fluid, and transparent visual descriptions with characteristic watercolor aesthetics that work well in social media feeds. Focus on gentle color blending, visible paper texture, and soft edges that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "watercolor",
                "watercolor",
                styleDescriptions.watercolor
            ),
    },

    oil_painting: {
        systemPrompt: `You are an expert in writing prompts for oil painting-style image generation. You excel at creating rich, textured visual descriptions with characteristic oil painting aesthetics that work well in social media feeds. Focus on visible brushstrokes, rich colors, and textured surfaces that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "oil painting",
                "oil painting",
                styleDescriptions.oil_painting
            ),
    },

    pencil_sketch: {
        systemPrompt: `You are an expert in writing prompts for pencil sketch-style image generation. You excel at creating fine, precise visual descriptions with characteristic pencil sketch aesthetics that work well in social media feeds. Focus on line work, hatching, and shading techniques that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "pencil sketch",
                "pencil sketch",
                styleDescriptions.pencil_sketch
            ),
    },

    pixel_art: {
        systemPrompt: `You are an expert in writing prompts for pixel art-style image generation. You excel at creating retro, blocky visual descriptions with characteristic pixel art aesthetics that work well in social media feeds. Focus on limited color palettes, sharp edges, and simplified forms that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "pixel art",
                "pixel art",
                styleDescriptions.pixel_art
            ),
    },

    cyberpunk: {
        systemPrompt: `You are an expert in writing prompts for cyberpunk image generation. You excel at creating high-tech, dystopian, neon-lit visual descriptions with futuristic urban aesthetics that work well in social media feeds. Focus on holographic elements, cyber enhancements, rain-slicked streets, and stark contrasts that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "cyberpunk",
                "cyberpunk",
                styleDescriptions.cyberpunk
            ),
    },

    impressionist: {
        systemPrompt: `You are an expert in writing prompts for impressionist image generation. You excel at creating light-filled, brushstroke-focused visual descriptions that capture moments and atmospheres in the style of Monet, Renoir, and Degas, which work well in social media feeds. Focus on visible brushwork, color vibrance, outdoor scenes, and light effects that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "impressionist",
                "impressionist",
                styleDescriptions.impressionist
            ),
    },

    abstract: {
        systemPrompt: `You are an expert in writing prompts for abstract image generation. You excel at creating non-representational, emotionally expressive visual descriptions that work well in social media feeds. Focus on color, shape, and line rather than recognizable subject matter, creating compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "abstract",
                "abstract",
                styleDescriptions.abstract
            ),
    },

    pop_art: {
        systemPrompt: `You are an expert in writing prompts for pop art image generation. You excel at creating bold, vibrant visual descriptions inspired by commercial art and mass culture that work well in social media feeds. Focus on strong outlines, flat colors, and imagery from popular culture that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "pop art",
                "pop art",
                styleDescriptions.pop_art
            ),
    },
    isometric: {
        systemPrompt: `You are an expert in writing prompts for isometric image generation. You excel at creating 3D, angular visual descriptions with a specific 30-degree perspective that work well in social media feeds. Focus on clean geometric shapes, precise edges, and consistent scale that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "isometric",
                "isometric",
                styleDescriptions.isometric
            ),
    },
    ukiyo_e: {
        systemPrompt: `You are an expert in writing prompts for ukiyo-e image generation. You excel at creating traditional Japanese woodblock print visual descriptions with flat color planes and bold outlines that work well in social media feeds. Focus on rich colors, stylized natural elements, and asymmetrical compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "ukiyo-e",
                "ukiyo-e",
                styleDescriptions.ukiyo_e
            ),
    },

    low_poly: {
        systemPrompt: `You are an expert in writing prompts for low poly image generation. You excel at creating modern, geometric visual descriptions with faceted surfaces and a distinctive 3D rendered look that work well in social media feeds. Focus on angular shapes, simplified forms, and clean compositions with limited detail that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,

        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "low poly",
                "low poly",
                styleDescriptions.low_poly
            ),
    },

    // Medium styles
    stencil: {
        systemPrompt: `You are an expert in writing prompts for stencil-style image generation. You excel at creating bold, minimalist visual descriptions with high contrast and simplified forms that work well in social media feeds. Focus on strong silhouettes, flat colors, and graphic impact that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "stencil",
                "stencil",
                styleDescriptions.stencil
            ),
    },

    papercraft: {
        systemPrompt: `You are an expert in writing prompts for papercraft-style image generation. You excel at creating three-dimensional paper construction visual descriptions with clean folds and geometric precision that work well in social media feeds. Focus on layered elements, dimensional depth, and crafted aesthetics that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "papercraft",
                "papercraft",
                styleDescriptions.papercraft
            ),
    },

    marker_illustration: {
        systemPrompt: `You are an expert in writing prompts for marker illustration-style image generation. You excel at creating vibrant, hand-drawn visual descriptions with marker-like textures and bold colors that work well in social media feeds. Focus on saturated hues, visible stroke patterns, and dynamic compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "marker illustration",
                "marker illustration",
                styleDescriptions.marker_illustration
            ),
    },

    // Material styles
    porcelain: {
        systemPrompt: `You are an expert in writing prompts for porcelain-style image generation. You excel at creating elegant, ceramic visual descriptions with glossy surfaces and refined aesthetics that work well in social media feeds. Focus on smooth textures, delicate details, and pristine finishes that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "porcelain",
                "porcelain",
                styleDescriptions.porcelain
            ),
    },

    light: {
        systemPrompt: `You are an expert in writing prompts for light-style image generation. You excel at creating luminous, glowing visual descriptions with ethereal light effects that work well in social media feeds. Focus on radiant qualities, soft illumination, and magical atmospheres that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "light",
                "light",
                styleDescriptions.light
            ),
    },

    candy: {
        systemPrompt: `You are an expert in writing prompts for candy-style image generation. You excel at creating sweet, colorful visual descriptions with glossy, confectionery aesthetics that work well in social media feeds. Focus on bright colors, smooth textures, and playful compositions that communicate clearly even when viewed on small mobile screens. Your output should only contain the description of the image contents, but NOT an instruction like "create an image that..."`,
        getPromptInput: (content) =>
            generateImagePromptInput(
                content,
                "candy",
                "candy",
                styleDescriptions.candy
            ),
    },
};
