#!/usr/bin/env node

/**
 * Quick script to generate placeholder SVG images for each image style
 * This creates immediate visual samples while the full generation script is being set up
 */

const fs = require('fs');
const path = require('path');

// Import the ImageStyles from the constants file
const ImageStyles = [
  { option: "none", label: "No Style" },
  // Medium styles
  { option: "stencil", label: "Stencil" },
  { option: "watercolor", label: "Watercolor" },
  { option: "papercraft", label: "Papercraft" },
  { option: "marker_illustration", label: "Marker Illustration" },
  { option: "risograph", label: "Risograph" },
  { option: "graffiti", label: "Graffiti" },
  { option: "ink_wash", label: "Ink Wash" },
  { option: "quilling", label: "Quilling" },
  { option: "charcoal", label: "Charcoal" },
  { option: "oil_painting", label: "Oil Painting" },
  { option: "collage", label: "Collage" },
  { option: "mosaic", label: "Mosaic" },
  // Material styles
  { option: "porcelain", label: "Porcelain" },
  { option: "light", label: "Light" },
  { option: "candy", label: "Candy" },
  { option: "bubbles", label: "Bubbles" },
  { option: "crystals", label: "Crystals" },
  { option: "ceramic", label: "Ceramic" },
  { option: "plastic", label: "Plastic" },
  { option: "wood", label: "Wood" },
  { option: "metal", label: "Metal" },
  { option: "water", label: "Water" },
  { option: "glass", label: "Glass" },
  { option: "sand", label: "Sand" },
  { option: "rain", label: "Rain" },
  // Photography styles
  { option: "high_key_photograph", label: "High Key Photo" },
  { option: "low_key_photograph", label: "Low Key Photo" },
  { option: "low_angle_photograph", label: "Low Angle Photo" },
  { option: "high_angle_photograph", label: "High Angle Photo" },
  { option: "extreme_close_up", label: "Extreme Close Up" },
  { option: "low_shutter_speed_photograph", label: "Low Shutter Speed" },
  { option: "bokeh_photograph", label: "Bokeh Photo" },
  { option: "silhouette_photograph", label: "Silhouette Photo" },
  { option: "studio_lighting", label: "Studio Lighting" },
  { option: "black_and_white_photograph", label: "B&W Photo" },
  { option: "birds_eye_view", label: "Bird's Eye View" },
  { option: "worms_eye_view", label: "Worm's Eye View" },
  { option: "dutch_angle", label: "Dutch Angle" },
  { option: "long_exposure_photograph", label: "Long Exposure" },
  // Lighting styles
  { option: "natural_lighting", label: "Natural Lighting" },
  { option: "light_and_shadow", label: "Light and Shadow" },
  { option: "volumetric_lighting", label: "Volumetric Lighting" },
  { option: "neon_lighting", label: "Neon Lighting" },
  { option: "golden_hour", label: "Golden Hour" },
  { option: "blue_hour", label: "Blue Hour" },
  { option: "backlighting", label: "Backlighting" },
  { option: "chiaroscuro", label: "Chiaroscuro" },
  { option: "god_rays", label: "God Rays" },
  { option: "candlelight", label: "Candlelight" },
  { option: "street_lighting", label: "Street Lighting" },
  { option: "softbox_lighting", label: "Softbox Lighting" },
  { option: "moonlight", label: "Moonlight" },
  { option: "fairy_lights", label: "Fairy Lights" },
  // Color and palette styles
  { option: "cool_tones", label: "Cool Tones" },
  { option: "warm_tones", label: "Warm Tones" },
  { option: "pastels", label: "Pastels" },
  { option: "vibrant", label: "Vibrant" },
  { option: "earth_tones", label: "Earth Tones" },
  { option: "jewel_tones", label: "Jewel Tones" },
  { option: "monochromatic_blues", label: "Monochromatic Blues" },
  { option: "earthy_reds_and_oranges", label: "Earthy Reds & Oranges" },
  { option: "neon_graffiti", label: "Neon Graffiti" },
  { option: "autumn_leaves", label: "Autumn Leaves" },
  { option: "deep_sea_blues", label: "Deep Sea Blues" },
  { option: "grayscale", label: "Grayscale" },
  { option: "sepia", label: "Sepia" },
  { option: "primary_colors", label: "Primary Colors" },
  { option: "rainbow_spectrum", label: "Rainbow Spectrum" },
  { option: "metallics", label: "Metallics" },
  // Original styles
  { option: "photorealistic", label: "Photorealistic" },
  { option: "pixel_art", label: "Pixel Art" },
  { option: "pencil_sketch", label: "Pencil Sketch" },
  { option: "cyberpunk", label: "Cyberpunk" },
  { option: "impressionist", label: "Impressionist" },
  { option: "abstract", label: "Abstract" },
  { option: "pop_art", label: "Pop Art" },
  { option: "isometric", label: "Isometric" },
  { option: "ukiyo_e", label: "Ukiyo-e" },
  { option: "low_poly", label: "Low Poly" },
];

// Configuration
const OUTPUT_DIR = path.join(__dirname, '..', 'public', 'images', 'style-samples');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Get color scheme for each style
 */
function getStyleColors(styleOption) {
  const colorSchemes = {
    none: { primary: '#6B7280', secondary: '#9CA3AF' },
    // Medium styles
    stencil: { primary: '#1F2937', secondary: '#374151' },
    watercolor: { primary: '#EC4899', secondary: '#F472B6' },
    papercraft: { primary: '#F59E0B', secondary: '#FCD34D' },
    marker_illustration: { primary: '#EF4444', secondary: '#F87171' },
    risograph: { primary: '#8B5CF6', secondary: '#A78BFA' },
    graffiti: { primary: '#84CC16', secondary: '#A3E635' },
    ink_wash: { primary: '#374151', secondary: '#6B7280' },
    quilling: { primary: '#EC4899', secondary: '#F472B6' },
    charcoal: { primary: '#111827', secondary: '#374151' },
    oil_painting: { primary: '#F59E0B', secondary: '#FCD34D' },
    collage: { primary: '#EF4444', secondary: '#F87171' },
    mosaic: { primary: '#10B981', secondary: '#34D399' },
    // Material styles
    porcelain: { primary: '#DBEAFE', secondary: '#FEF3C7' },
    light: { primary: '#FEF3C7', secondary: '#FBBF24' },
    candy: { primary: '#EC4899', secondary: '#F472B6' },
    bubbles: { primary: '#BFDBFE', secondary: '#93C5FD' },
    crystals: { primary: '#C084FC', secondary: '#DDD6FE' },
    ceramic: { primary: '#FED7AA', secondary: '#FDBA74' },
    plastic: { primary: '#86EFAC', secondary: '#BBF7D0' },
    wood: { primary: '#D97706', secondary: '#F59E0B' },
    metal: { primary: '#9CA3AF', secondary: '#D1D5DB' },
    water: { primary: '#60A5FA', secondary: '#93C5FD' },
    glass: { primary: '#A5F3FC', secondary: '#67E8F9' },
    sand: { primary: '#FBBF24', secondary: '#FCD34D' },
    rain: { primary: '#6B7280', secondary: '#9CA3AF' },
    // Photography styles
    high_key_photograph: { primary: '#F9FAFB', secondary: '#F3F4F6' },
    low_key_photograph: { primary: '#111827', secondary: '#1F2937' },
    low_angle_photograph: { primary: '#3730A3', secondary: '#5B21B6' },
    high_angle_photograph: { primary: '#059669', secondary: '#0D9488' },
    extreme_close_up: { primary: '#DC2626', secondary: '#EF4444' },
    low_shutter_speed_photograph: { primary: '#5B21B6', secondary: '#7C3AED' },
    bokeh_photograph: { primary: '#F59E0B', secondary: '#FCD34D' },
    silhouette_photograph: { primary: '#F59E0B', secondary: '#111827' },
    studio_lighting: { primary: '#D1D5DB', secondary: '#F3F4F6' },
    black_and_white_photograph: { primary: '#6B7280', secondary: '#9CA3AF' },
    birds_eye_view: { primary: '#059669', secondary: '#0EA5E9' },
    worms_eye_view: { primary: '#A16207', secondary: '#D97706' },
    dutch_angle: { primary: '#7C2D12', secondary: '#DC2626' },
    long_exposure_photograph: { primary: '#4338CA', secondary: '#7C3AED' },
    // Lighting styles
    natural_lighting: { primary: '#FCD34D', secondary: '#F59E0B' },
    light_and_shadow: { primary: '#FCD34D', secondary: '#374151' },
    volumetric_lighting: { primary: '#93C5FD', secondary: '#A78BFA' },
    neon_lighting: { primary: '#EC4899', secondary: '#06B6D4' },
    golden_hour: { primary: '#FCD34D', secondary: '#F59E0B' },
    blue_hour: { primary: '#3B82F6', secondary: '#4338CA' },
    backlighting: { primary: '#FCD34D', secondary: '#F59E0B' },
    chiaroscuro: { primary: '#FEF3C7', secondary: '#374151' },
    god_rays: { primary: '#FCD34D', secondary: '#3B82F6' },
    candlelight: { primary: '#F59E0B', secondary: '#DC2626' },
    street_lighting: { primary: '#F59E0B', secondary: '#6B7280' },
    softbox_lighting: { primary: '#F3F4F6', secondary: '#D1D5DB' },
    moonlight: { primary: '#93C5FD', secondary: '#6B7280' },
    fairy_lights: { primary: '#FCD34D', secondary: '#EC4899' },
    // Color and palette styles
    cool_tones: { primary: '#3B82F6', secondary: '#06B6D4' },
    warm_tones: { primary: '#F59E0B', secondary: '#EF4444' },
    pastels: { primary: '#FBCFE8', secondary: '#DDD6FE' },
    vibrant: { primary: '#EF4444', secondary: '#FCD34D' },
    earth_tones: { primary: '#D97706', secondary: '#059669' },
    jewel_tones: { primary: '#059669', secondary: '#7C3AED' },
    monochromatic_blues: { primary: '#93C5FD', secondary: '#1D4ED8' },
    earthy_reds_and_oranges: { primary: '#DC2626', secondary: '#F59E0B' },
    neon_graffiti: { primary: '#84CC16', secondary: '#EC4899' },
    autumn_leaves: { primary: '#FCD34D', secondary: '#DC2626' },
    deep_sea_blues: { primary: '#1E40AF', secondary: '#3730A3' },
    grayscale: { primary: '#9CA3AF', secondary: '#6B7280' },
    sepia: { primary: '#A16207', secondary: '#D97706' },
    primary_colors: { primary: '#EF4444', secondary: '#3B82F6' },
    rainbow_spectrum: { primary: '#EF4444', secondary: '#3B82F6' },
    metallics: { primary: '#9CA3AF', secondary: '#FCD34D' },
    // Original styles
    photorealistic: { primary: '#3B82F6', secondary: '#1D4ED8' },
    pixel_art: { primary: '#10B981', secondary: '#059669' },
    pencil_sketch: { primary: '#D1D5DB', secondary: '#9CA3AF' },
    cyberpunk: { primary: '#A855F7', secondary: '#EC4899' },
    impressionist: { primary: '#FCD34D', secondary: '#F59E0B' },
    abstract: { primary: '#EF4444', secondary: '#A855F7' },
    pop_art: { primary: '#06B6D4', secondary: '#EC4899' },
    isometric: { primary: '#14B8A6', secondary: '#3B82F6' },
    ukiyo_e: { primary: '#EC4899', secondary: '#EF4444' },
    low_poly: { primary: '#8B5CF6', secondary: '#A855F7' },
  };

  return colorSchemes[styleOption] || { primary: '#6B7280', secondary: '#9CA3AF' };
}

/**
 * Get icon for each style
 */
function getStyleIcon(styleOption) {
  const icons = {
    none: '🚫', stencil: '🎯', watercolor: '🎨', papercraft: '📄',
    marker_illustration: '🖊️', risograph: '🖨️', graffiti: '🎨', ink_wash: '🖋️',
    quilling: '🌀', charcoal: '⚫', oil_painting: '🖼️', collage: '📰', mosaic: '🧩',
    porcelain: '🏺', light: '💡', candy: '🍭', bubbles: '🫧', crystals: '💎',
    ceramic: '🏺', plastic: '🧱', wood: '🪵', metal: '⚙️', water: '💧',
    glass: '🔍', sand: '🏖️', rain: '🌧️', high_key_photograph: '☀️',
    low_key_photograph: '🌙', low_angle_photograph: '📐', high_angle_photograph: '🔺',
    extreme_close_up: '🔍', low_shutter_speed_photograph: '💫', bokeh_photograph: '✨',
    silhouette_photograph: '👤', studio_lighting: '💡', black_and_white_photograph: '⚫',
    birds_eye_view: '🦅', worms_eye_view: '🪱', dutch_angle: '📐',
    long_exposure_photograph: '🌟', natural_lighting: '☀️', light_and_shadow: '🌗',
    volumetric_lighting: '🌫️', neon_lighting: '🌈', golden_hour: '🌅',
    blue_hour: '🌆', backlighting: '💡', chiaroscuro: '🎭', god_rays: '☀️',
    candlelight: '🕯️', street_lighting: '🏮', softbox_lighting: '📦',
    moonlight: '🌙', fairy_lights: '✨', cool_tones: '❄️', warm_tones: '🔥',
    pastels: '🌸', vibrant: '🌈', earth_tones: '🌍', jewel_tones: '💎',
    monochromatic_blues: '🔵', earthy_reds_and_oranges: '🍂', neon_graffiti: '🌈',
    autumn_leaves: '🍁', deep_sea_blues: '🌊', grayscale: '⚫', sepia: '🟤',
    primary_colors: '🔴', rainbow_spectrum: '🌈', metallics: '⚙️',
    photorealistic: '📷', pixel_art: '🎮', pencil_sketch: '✏️', cyberpunk: '🤖',
    impressionist: '🌅', abstract: '🎭', pop_art: '💥', isometric: '📐',
    ukiyo_e: '🌸', low_poly: '💎',
  };
  return icons[styleOption] || '🎨';
}

/**
 * Create SVG placeholder for a style
 */
function createStyleSVG(style) {
  const colors = getStyleColors(style.option);
  const icon = getStyleIcon(style.option);
  
  return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grad-${style.option}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${colors.secondary};stop-opacity:0.8" />
      </linearGradient>
      <filter id="shadow-${style.option}">
        <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
      </filter>
    </defs>
    <rect width="400" height="300" fill="url(#grad-${style.option})"/>
    <rect x="20" y="20" width="360" height="260" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2" rx="10"/>
    <text x="200" y="120" font-family="Arial, sans-serif" font-size="48" fill="white" text-anchor="middle" dominant-baseline="middle" filter="url(#shadow-${style.option})">${icon}</text>
    <text x="200" y="180" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle" filter="url(#shadow-${style.option})">${style.label}</text>
    <text x="200" y="210" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)" text-anchor="middle" dominant-baseline="middle">Style Preview</text>
  </svg>`;
}

/**
 * Generate all placeholder SVGs
 */
function generatePlaceholders() {
  console.log('Generating placeholder SVG images...');
  console.log(`Output directory: ${OUTPUT_DIR}`);
  
  let generated = 0;
  let skipped = 0;
  
  ImageStyles.forEach((style, index) => {
    const filename = `${style.option}.svg`;
    const filepath = path.join(OUTPUT_DIR, filename);
    
    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      console.log(`⏭️  ${style.label} (${index + 1}/${ImageStyles.length}) - Already exists`);
      skipped++;
      return;
    }
    
    try {
      const svg = createStyleSVG(style);
      fs.writeFileSync(filepath, svg);
      console.log(`✅ ${style.label} (${index + 1}/${ImageStyles.length}) - Generated`);
      generated++;
    } catch (error) {
      console.log(`❌ ${style.label} (${index + 1}/${ImageStyles.length}) - Error: ${error.message}`);
    }
  });
  
  console.log('\n=== Generation Summary ===');
  console.log(`✅ Generated: ${generated}`);
  console.log(`⏭️  Skipped: ${skipped}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);
  console.log('\n🎉 Placeholder generation complete!');
}

// Run the script
if (require.main === module) {
  generatePlaceholders();
}

module.exports = { generatePlaceholders };
