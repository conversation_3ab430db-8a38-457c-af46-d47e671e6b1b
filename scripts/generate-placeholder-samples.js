#!/usr/bin/env node

/**
 * Quick script to generate placeholder SVG images for each image style
 */

const fs = require('fs');
const path = require('path');

// Basic image styles
const ImageStyles = [
  { option: "none", label: "No Style" },
  { option: "stencil", label: "Stencil" },
  { option: "watercolor", label: "Watercolor" },
  { option: "papercraft", label: "Papercraft" },
  { option: "photorealistic", label: "Photorealistic" },
  { option: "pixel_art", label: "Pixel Art" },
  { option: "pencil_sketch", label: "Pencil Sketch" },
  { option: "cyberpunk", label: "Cyberpunk" },
  { option: "impressionist", label: "Impressionist" },
  { option: "abstract", label: "Abstract" },
  { option: "pop_art", label: "Pop Art" },
  { option: "isometric", label: "Isometric" },
];

// Configuration
const OUTPUT_DIR = path.join(__dirname, '..', 'public', 'images', 'style-samples');

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

function getStyleColors(styleOption) {
  const colorSchemes = {
    none: { primary: '#6B7280', secondary: '#9CA3AF' },
    stencil: { primary: '#1F2937', secondary: '#374151' },
    watercolor: { primary: '#EC4899', secondary: '#F472B6' },
    papercraft: { primary: '#F59E0B', secondary: '#FCD34D' },
    photorealistic: { primary: '#3B82F6', secondary: '#1D4ED8' },
    pixel_art: { primary: '#10B981', secondary: '#059669' },
    pencil_sketch: { primary: '#D1D5DB', secondary: '#9CA3AF' },
    cyberpunk: { primary: '#A855F7', secondary: '#EC4899' },
    impressionist: { primary: '#FCD34D', secondary: '#F59E0B' },
    abstract: { primary: '#EF4444', secondary: '#A855F7' },
    pop_art: { primary: '#06B6D4', secondary: '#EC4899' },
    isometric: { primary: '#14B8A6', secondary: '#3B82F6' },
  };
  return colorSchemes[styleOption] || { primary: '#6B7280', secondary: '#9CA3AF' };
}

function getStyleIcon(styleOption) {
  const icons = {
    none: '🚫', stencil: '🎯', watercolor: '🎨', papercraft: '📄',
    photorealistic: '📷', pixel_art: '🎮', pencil_sketch: '✏️', cyberpunk: '🤖',
    impressionist: '🌅', abstract: '🎭', pop_art: '💥', isometric: '📐',
  };
  return icons[styleOption] || '🎨';
}

function createStyleSVG(style) {
  const colors = getStyleColors(style.option);
  const icon = getStyleIcon(style.option);
  
  return `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grad-${style.option}" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${colors.primary};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${colors.secondary};stop-opacity:0.8" />
      </linearGradient>
    </defs>
    <rect width="400" height="300" fill="url(#grad-${style.option})"/>
    <rect x="20" y="20" width="360" height="260" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2" rx="10"/>
    <text x="200" y="120" font-family="Arial, sans-serif" font-size="48" fill="white" text-anchor="middle" dominant-baseline="middle">${icon}</text>
    <text x="200" y="180" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">${style.label}</text>
    <text x="200" y="210" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)" text-anchor="middle" dominant-baseline="middle">Style Preview</text>
  </svg>`;
}

function generatePlaceholders() {
  console.log('Generating placeholder SVG images...');
  console.log(`Output directory: ${OUTPUT_DIR}`);
  
  let generated = 0;
  
  ImageStyles.forEach((style, index) => {
    const filename = `${style.option}.svg`;
    const filepath = path.join(OUTPUT_DIR, filename);
    
    try {
      const svg = createStyleSVG(style);
      fs.writeFileSync(filepath, svg);
      console.log(`✅ ${style.label} (${index + 1}/${ImageStyles.length}) - Generated`);
      generated++;
    } catch (error) {
      console.log(`❌ ${style.label} (${index + 1}/${ImageStyles.length}) - Error: ${error.message}`);
    }
  });
  
  console.log(`\n🎉 Generated ${generated} placeholder images!`);
}

generatePlaceholders();
