#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to generate sample images for each image style
 * This script will create representative images for each style in the ImageStyles array
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

// Import the ImageStyles from the constants file
const ImageStyles = [
  { option: "none", label: "No Style" },
  // Medium styles
  { option: "stencil", label: "Stencil" },
  { option: "watercolor", label: "Watercolor" },
  { option: "papercraft", label: "Papercraft" },
  { option: "marker_illustration", label: "Marker Illustration" },
  { option: "risograph", label: "Risograph" },
  { option: "graffiti", label: "Graffiti" },
  { option: "ink_wash", label: "Ink Wash" },
  { option: "quilling", label: "Quilling" },
  { option: "charcoal", label: "Charcoal" },
  { option: "oil_painting", label: "Oil Painting" },
  { option: "collage", label: "Collage" },
  { option: "mosaic", label: "Mosaic" },
  // Material styles
  { option: "porcelain", label: "Porcelain" },
  { option: "light", label: "Light" },
  { option: "candy", label: "Candy" },
  { option: "bubbles", label: "Bubbles" },
  { option: "crystals", label: "Crystals" },
  { option: "ceramic", label: "Ceramic" },
  { option: "plastic", label: "Plastic" },
  { option: "wood", label: "Wood" },
  { option: "metal", label: "Metal" },
  { option: "water", label: "Water" },
  { option: "glass", label: "Glass" },
  { option: "sand", label: "Sand" },
  { option: "rain", label: "Rain" },
  // Photography styles
  { option: "high_key_photograph", label: "High Key Photo" },
  { option: "low_key_photograph", label: "Low Key Photo" },
  { option: "low_angle_photograph", label: "Low Angle Photo" },
  { option: "high_angle_photograph", label: "High Angle Photo" },
  { option: "extreme_close_up", label: "Extreme Close Up" },
  { option: "low_shutter_speed_photograph", label: "Low Shutter Speed" },
  { option: "bokeh_photograph", label: "Bokeh Photo" },
  { option: "silhouette_photograph", label: "Silhouette Photo" },
  { option: "studio_lighting", label: "Studio Lighting" },
  { option: "black_and_white_photograph", label: "B&W Photo" },
  { option: "birds_eye_view", label: "Bird's Eye View" },
  { option: "worms_eye_view", label: "Worm's Eye View" },
  { option: "dutch_angle", label: "Dutch Angle" },
  { option: "long_exposure_photograph", label: "Long Exposure" },
  // Lighting styles
  { option: "natural_lighting", label: "Natural Lighting" },
  { option: "light_and_shadow", label: "Light and Shadow" },
  { option: "volumetric_lighting", label: "Volumetric Lighting" },
  { option: "neon_lighting", label: "Neon Lighting" },
  { option: "golden_hour", label: "Golden Hour" },
  { option: "blue_hour", label: "Blue Hour" },
  { option: "backlighting", label: "Backlighting" },
  { option: "chiaroscuro", label: "Chiaroscuro" },
  { option: "god_rays", label: "God Rays" },
  { option: "candlelight", label: "Candlelight" },
  { option: "street_lighting", label: "Street Lighting" },
  { option: "softbox_lighting", label: "Softbox Lighting" },
  { option: "moonlight", label: "Moonlight" },
  { option: "fairy_lights", label: "Fairy Lights" },
  // Color and palette styles
  { option: "cool_tones", label: "Cool Tones" },
  { option: "warm_tones", label: "Warm Tones" },
  { option: "pastels", label: "Pastels" },
  { option: "vibrant", label: "Vibrant" },
  { option: "earth_tones", label: "Earth Tones" },
  { option: "jewel_tones", label: "Jewel Tones" },
  { option: "monochromatic_blues", label: "Monochromatic Blues" },
  { option: "earthy_reds_and_oranges", label: "Earthy Reds & Oranges" },
  { option: "neon_graffiti", label: "Neon Graffiti" },
  { option: "autumn_leaves", label: "Autumn Leaves" },
  { option: "deep_sea_blues", label: "Deep Sea Blues" },
  { option: "grayscale", label: "Grayscale" },
  { option: "sepia", label: "Sepia" },
  { option: "primary_colors", label: "Primary Colors" },
  { option: "rainbow_spectrum", label: "Rainbow Spectrum" },
  { option: "metallics", label: "Metallics" },
  // Original styles
  { option: "photorealistic", label: "Photorealistic" },
  { option: "pixel_art", label: "Pixel Art" },
  { option: "pencil_sketch", label: "Pencil Sketch" },
  { option: "cyberpunk", label: "Cyberpunk" },
  { option: "impressionist", label: "Impressionist" },
  { option: "abstract", label: "Abstract" },
  { option: "pop_art", label: "Pop Art" },
  { option: "isometric", label: "Isometric" },
  { option: "ukiyo_e", label: "Ukiyo-e" },
  { option: "low_poly", label: "Low Poly" },
];

// Configuration
const OUTPUT_DIR = path.join(__dirname, '..', 'public', 'images', 'style-samples');
const AGENT_URL = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
const SAMPLE_PROMPT = "A beautiful mountain landscape with a lake";
const USE_FALLBACK_IMAGES = true; // Set to true to generate placeholder images when API fails

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Download file from URL
 */
function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    const file = fs.createWriteStream(filepath);

    protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve();
      });

      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', reject);
  });
}

/**
 * Create a simple SVG placeholder image for a style
 */
function createPlaceholderImage(style, filepath) {
  const getStyleColor = (styleOption) => {
    const colorMap = {
      none: '#6B7280',
      stencil: '#1F2937',
      watercolor: '#EC4899',
      papercraft: '#F59E0B',
      marker_illustration: '#EF4444',
      risograph: '#8B5CF6',
      graffiti: '#84CC16',
      ink_wash: '#374151',
      quilling: '#EC4899',
      charcoal: '#111827',
      oil_painting: '#F59E0B',
      collage: '#EF4444',
      mosaic: '#10B981',
      porcelain: '#DBEAFE',
      light: '#FEF3C7',
      candy: '#EC4899',
      bubbles: '#BFDBFE',
      crystals: '#C084FC',
      ceramic: '#FED7AA',
      plastic: '#86EFAC',
      wood: '#D97706',
      metal: '#9CA3AF',
      water: '#60A5FA',
      glass: '#A5F3FC',
      sand: '#FBBF24',
      rain: '#6B7280',
      photorealistic: '#3B82F6',
      pixel_art: '#10B981',
      pencil_sketch: '#9CA3AF',
      cyberpunk: '#A855F7',
      impressionist: '#FBBF24',
      abstract: '#EF4444',
      pop_art: '#06B6D4',
      isometric: '#14B8A6',
      ukiyo_e: '#EC4899',
      low_poly: '#8B5CF6',
    };
    return colorMap[styleOption] || '#6B7280';
  };

  const color = getStyleColor(style.option);
  const svg = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${color};stop-opacity:0.6" />
      </linearGradient>
    </defs>
    <rect width="400" height="300" fill="url(#grad)"/>
    <text x="200" y="150" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle" dominant-baseline="middle">${style.label}</text>
    <text x="200" y="180" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)" text-anchor="middle" dominant-baseline="middle">Sample Preview</text>
  </svg>`;

  fs.writeFileSync(filepath.replace('.jpg', '.svg'), svg);
  return filepath.replace('.jpg', '.svg');
}

/**
 * Generate image using the API
 */
async function generateImage(style, agentId = 'sample-agent') {
  const endpoint = `${AGENT_URL}/${agentId}/post-image-gen`;

  const requestBody = {
    description: SAMPLE_PROMPT,
    planId: 'style-samples',
  };

  if (style.option !== 'none') {
    requestBody.style = style.option;
  }

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.filepath;
  } catch (error) {
    console.error(`Failed to generate image for ${style.label}:`, error.message);
    return null;
  }
}

/**
 * Main function to generate all sample images
 */
async function generateAllSamples() {
  console.log('Starting sample image generation...');
  console.log(`Output directory: ${OUTPUT_DIR}`);

  const results = [];

  for (let i = 0; i < ImageStyles.length; i++) {
    const style = ImageStyles[i];
    const filename = `${style.option}.jpg`;
    const filepath = path.join(OUTPUT_DIR, filename);

    // Skip if file already exists
    if (fs.existsSync(filepath)) {
      console.log(`✓ ${style.label} (${i + 1}/${ImageStyles.length}) - Already exists`);
      results.push({ style, success: true, filepath: filename });
      continue;
    }

    console.log(`⏳ Generating ${style.label} (${i + 1}/${ImageStyles.length})...`);

    try {
      const imageUrl = await generateImage(style);

      if (imageUrl) {
        // Download the image
        const fullImageUrl = imageUrl.startsWith('http') ? imageUrl : `${AGENT_URL}${imageUrl}`;
        await downloadFile(fullImageUrl, filepath);

        console.log(`✓ ${style.label} - Generated successfully`);
        results.push({ style, success: true, filepath: filename, type: 'generated' });
      } else {
        throw new Error('Generation failed');
      }
    } catch (error) {
      console.log(`⚠ ${style.label} - API failed: ${error.message}`);

      if (USE_FALLBACK_IMAGES) {
        try {
          const placeholderPath = createPlaceholderImage(style, filepath);
          console.log(`✓ ${style.label} - Created placeholder`);
          results.push({
            style,
            success: true,
            filepath: path.basename(placeholderPath),
            type: 'placeholder'
          });
        } catch (placeholderError) {
          console.log(`✗ ${style.label} - Placeholder creation failed: ${placeholderError.message}`);
          results.push({ style, success: false, error: error.message });
        }
      } else {
        results.push({ style, success: false, error: error.message });
      }
    }

    // Add delay between requests to avoid rate limiting
    if (i < ImageStyles.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // Generate summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  console.log('\n=== Generation Summary ===');
  console.log(`✓ Successful: ${successful}`);
  console.log(`✗ Failed: ${failed}`);
  console.log(`📁 Output directory: ${OUTPUT_DIR}`);

  if (failed > 0) {
    console.log('\nFailed styles:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.style.label}: ${r.error}`);
    });
  }

  // Save metadata
  const metadata = {
    generated_at: new Date().toISOString(),
    total_styles: ImageStyles.length,
    successful: successful,
    failed: failed,
    results: results,
  };

  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'metadata.json'),
    JSON.stringify(metadata, null, 2)
  );

  console.log('\n✓ Metadata saved to metadata.json');
}

// Run the script
if (require.main === module) {
  generateAllSamples().catch(console.error);
}

module.exports = { generateAllSamples, ImageStyles };
