# Image Style Sample Generation Scripts

This directory contains scripts to automatically generate sample images for each image style used in the canvas editor.

## Overview

The canvas editor now features a tile-based design where each image style is represented by a visual sample. This provides users with a clear preview of what each style looks like before selecting it.

## Scripts

### 1. `generate-placeholder-samples.js`

**Purpose**: Quickly generates SVG placeholder images for immediate use.

**Usage**:
```bash
npm run generate-placeholder-samples
```

**Features**:
- Creates colorful SVG placeholders with gradients
- Includes style icons and labels
- Fast generation (completes in seconds)
- Perfect for development and testing

**Output**: SVG files in `public/images/style-samples/`

### 2. `generate-style-samples.js`

**Purpose**: Generates actual AI-generated sample images using the image generation API.

**Usage**:
```bash
# Generate using AI (requires running agent)
npm run generate-style-samples

# Generate with fallback placeholders if AI fails
npm run generate-style-samples:fallback
```

**Features**:
- Uses the actual image generation API
- Creates real sample images for each style
- Includes fallback to SVG placeholders if API fails
- Supports rate limiting and error handling
- Saves metadata about generation results

**Requirements**:
- Running agent server (NEXT_PUBLIC_AGENT_URL)
- Valid agent ID for image generation
- Internet connection for API calls

**Output**: JPG/PNG files in `public/images/style-samples/`

## File Structure

```
public/images/style-samples/
├── none.svg                    # No style placeholder
├── watercolor.svg             # Watercolor style placeholder
├── photorealistic.svg         # Photorealistic style placeholder
├── ...                        # Other style placeholders
├── metadata.json              # Generation metadata (from AI script)
└── README.md                  # This file
```

## Integration with Canvas Editor

The canvas editor automatically uses these sample images:

1. **Fallback Chain**: 
   - First tries to load AI-generated images (JPG/PNG)
   - Falls back to SVG placeholders
   - Finally falls back to CSS gradients

2. **Tile Design**: 
   - Each style is displayed as a visual tile
   - Includes sample image, icon, and label
   - Hover effects and selection indicators

3. **Performance**: 
   - Images are served statically from `/public`
   - SVG placeholders are lightweight
   - Lazy loading for better performance

## Customization

### Adding New Styles

1. Add the new style to `ImageStyles` array in both scripts
2. Add color scheme in `getStyleColors()` function
3. Add icon in `getStyleIcon()` function
4. Run the generation scripts

### Updating Sample Prompt

Edit the `SAMPLE_PROMPT` constant in `generate-style-samples.js`:

```javascript
const SAMPLE_PROMPT = "Your custom sample description";
```

### Changing Output Directory

Modify the `OUTPUT_DIR` constant in both scripts:

```javascript
const OUTPUT_DIR = path.join(__dirname, '..', 'your', 'custom', 'path');
```

## Troubleshooting

### Common Issues

1. **"Module not found" error**:
   - Ensure you're running from the project root
   - Check that the scripts directory exists

2. **API generation fails**:
   - Verify agent server is running
   - Check NEXT_PUBLIC_AGENT_URL environment variable
   - Use the fallback option: `npm run generate-style-samples:fallback`

3. **Permission errors**:
   - Ensure write permissions to `public/images/style-samples/`
   - Check disk space availability

4. **Images not showing in UI**:
   - Clear browser cache
   - Check browser console for 404 errors
   - Verify file paths in developer tools

### Debugging

Enable verbose logging by setting environment variable:
```bash
DEBUG=true npm run generate-style-samples
```

## Development Workflow

1. **Initial Setup**: Run `npm run generate-placeholder-samples` for immediate visual feedback
2. **Production Ready**: Run `npm run generate-style-samples` to create high-quality AI samples
3. **Updates**: Re-run scripts when adding new styles or changing prompts
4. **Testing**: Use placeholder script during development for faster iteration

## Performance Considerations

- **SVG Placeholders**: ~2-5KB each, very fast to load
- **AI Generated Images**: ~50-200KB each, higher quality but slower
- **Caching**: Images are cached by the browser
- **CDN Ready**: All images can be served from CDN for production

## Future Enhancements

- [ ] Automatic style detection from constants file
- [ ] Batch processing with progress indicators
- [ ] Image optimization and compression
- [ ] Multiple sample images per style
- [ ] Dynamic style preview generation
- [ ] Integration with design system tokens
