/**
 * Utility functions for managing sample images for image styles
 */

import { ImageStyles } from '@/common/constants';

export interface StyleSample {
  option: string;
  label: string;
  sampleImageUrl: string;
  isPlaceholder: boolean;
}

/**
 * Get the sample image URL for a given style
 */
export function getSampleImageUrl(styleOption: string): string {
  const baseUrl = '/images/style-samples';
  
  // First try to get the generated image
  const jpgUrl = `${baseUrl}/${styleOption}.jpg`;
  const svgUrl = `${baseUrl}/${styleOption}.svg`;
  
  // In a real implementation, you might want to check if the file exists
  // For now, we'll assume SVG placeholders exist for all styles
  return svgUrl;
}

/**
 * Check if a sample image exists (placeholder implementation)
 */
export async function sampleImageExists(styleOption: string): Promise<boolean> {
  try {
    const jpgUrl = getSampleImageUrl(styleOption).replace('.svg', '.jpg');
    const response = await fetch(jpgUrl, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Get all style samples with their image URLs
 */
export function getAllStyleSamples(): StyleSample[] {
  return ImageStyles.map(style => ({
    option: style.option,
    label: style.label,
    sampleImageUrl: getSampleImageUrl(style.option),
    isPlaceholder: true, // Will be updated when real images are available
  }));
}

/**
 * Get style samples organized by category
 */
export function getStyleSamplesByCategory() {
  const categories = {
    'No Style': ImageStyles.filter(s => s.option === 'none'),
    'Medium Styles': ImageStyles.filter(s => [
      'stencil', 'watercolor', 'papercraft', 'marker_illustration', 'risograph',
      'graffiti', 'ink_wash', 'quilling', 'charcoal', 'oil_painting', 'collage', 'mosaic'
    ].includes(s.option)),
    'Material Styles': ImageStyles.filter(s => [
      'porcelain', 'light', 'candy', 'bubbles', 'crystals', 'ceramic',
      'plastic', 'wood', 'metal', 'water', 'glass', 'sand', 'rain'
    ].includes(s.option)),
    'Photography': ImageStyles.filter(s => [
      'high_key_photograph', 'low_key_photograph', 'low_angle_photograph',
      'high_angle_photograph', 'extreme_close_up', 'low_shutter_speed_photograph',
      'bokeh_photograph', 'silhouette_photograph', 'studio_lighting',
      'black_and_white_photograph', 'birds_eye_view', 'worms_eye_view',
      'dutch_angle', 'long_exposure_photograph'
    ].includes(s.option)),
    'Lighting': ImageStyles.filter(s => [
      'natural_lighting', 'light_and_shadow', 'volumetric_lighting', 'neon_lighting',
      'golden_hour', 'blue_hour', 'backlighting', 'chiaroscuro', 'god_rays',
      'candlelight', 'street_lighting', 'softbox_lighting', 'moonlight', 'fairy_lights'
    ].includes(s.option)),
    'Color & Palette': ImageStyles.filter(s => [
      'cool_tones', 'warm_tones', 'pastels', 'vibrant', 'earth_tones',
      'jewel_tones', 'monochromatic_blues', 'earthy_reds_and_oranges',
      'neon_graffiti', 'autumn_leaves', 'deep_sea_blues', 'grayscale',
      'sepia', 'primary_colors', 'rainbow_spectrum', 'metallics'
    ].includes(s.option)),
    'Artistic Styles': ImageStyles.filter(s => [
      'photorealistic', 'pixel_art', 'pencil_sketch', 'cyberpunk',
      'impressionist', 'abstract', 'pop_art', 'isometric', 'ukiyo_e', 'low_poly'
    ].includes(s.option)),
  };

  return Object.entries(categories).map(([categoryName, styles]) => ({
    category: categoryName,
    styles: styles.map(style => ({
      option: style.option,
      label: style.label,
      sampleImageUrl: getSampleImageUrl(style.option),
      isPlaceholder: true,
    }))
  }));
}

/**
 * Get featured/popular style samples for quick access
 */
export function getFeaturedStyleSamples(): StyleSample[] {
  const featuredStyles = [
    'none',
    'photorealistic', 
    'watercolor',
    'pencil_sketch',
    'cyberpunk',
    'pop_art',
    'oil_painting',
    'pixel_art'
  ];

  return featuredStyles
    .map(option => ImageStyles.find(s => s.option === option))
    .filter(Boolean)
    .map(style => ({
      option: style!.option,
      label: style!.label,
      sampleImageUrl: getSampleImageUrl(style!.option),
      isPlaceholder: true,
    }));
}

/**
 * Create a fallback gradient for a style when no image is available
 */
export function getStyleGradient(styleOption: string): string {
  const gradientMap: Record<string, string> = {
    none: 'from-gray-400 to-gray-600',
    // Medium styles
    stencil: 'from-gray-800 to-black',
    watercolor: 'from-pink-300 to-purple-400',
    papercraft: 'from-orange-200 to-yellow-300',
    marker_illustration: 'from-red-400 to-pink-500',
    risograph: 'from-blue-400 to-purple-500',
    graffiti: 'from-lime-400 to-green-600',
    ink_wash: 'from-gray-600 to-gray-800',
    quilling: 'from-pink-400 to-rose-500',
    charcoal: 'from-gray-700 to-gray-900',
    oil_painting: 'from-amber-500 to-orange-600',
    collage: 'from-yellow-400 to-red-500',
    mosaic: 'from-blue-500 to-green-500',
    // Material styles
    porcelain: 'from-blue-100 to-white',
    light: 'from-yellow-200 to-white',
    candy: 'from-pink-400 to-red-400',
    bubbles: 'from-blue-200 to-cyan-300',
    crystals: 'from-purple-300 to-blue-400',
    ceramic: 'from-orange-200 to-red-300',
    plastic: 'from-green-300 to-blue-400',
    wood: 'from-amber-600 to-orange-700',
    metal: 'from-gray-400 to-gray-600',
    water: 'from-blue-400 to-cyan-500',
    glass: 'from-cyan-200 to-blue-300',
    sand: 'from-yellow-600 to-orange-500',
    rain: 'from-gray-500 to-blue-600',
    // Photography styles
    high_key_photograph: 'from-gray-100 to-white',
    low_key_photograph: 'from-gray-800 to-black',
    low_angle_photograph: 'from-blue-600 to-purple-700',
    high_angle_photograph: 'from-green-500 to-blue-600',
    extreme_close_up: 'from-red-500 to-pink-600',
    low_shutter_speed_photograph: 'from-purple-600 to-blue-700',
    bokeh_photograph: 'from-orange-400 to-yellow-500',
    silhouette_photograph: 'from-orange-500 to-black',
    studio_lighting: 'from-gray-300 to-gray-500',
    black_and_white_photograph: 'from-gray-600 to-gray-800',
    birds_eye_view: 'from-green-400 to-blue-500',
    worms_eye_view: 'from-brown-400 to-orange-600',
    dutch_angle: 'from-purple-500 to-red-600',
    long_exposure_photograph: 'from-indigo-600 to-purple-700',
    // Lighting styles
    natural_lighting: 'from-yellow-300 to-orange-400',
    light_and_shadow: 'from-yellow-400 to-gray-700',
    volumetric_lighting: 'from-blue-300 to-purple-500',
    neon_lighting: 'from-pink-500 to-cyan-400',
    golden_hour: 'from-yellow-400 to-orange-500',
    blue_hour: 'from-blue-500 to-indigo-600',
    backlighting: 'from-yellow-300 to-orange-600',
    chiaroscuro: 'from-yellow-200 to-gray-800',
    god_rays: 'from-yellow-300 to-blue-500',
    candlelight: 'from-orange-400 to-red-600',
    street_lighting: 'from-orange-500 to-gray-700',
    softbox_lighting: 'from-gray-200 to-gray-400',
    moonlight: 'from-blue-300 to-gray-600',
    fairy_lights: 'from-yellow-300 to-pink-400',
    // Color and palette styles
    cool_tones: 'from-blue-400 to-cyan-500',
    warm_tones: 'from-orange-400 to-red-500',
    pastels: 'from-pink-200 to-purple-300',
    vibrant: 'from-red-500 to-yellow-400',
    earth_tones: 'from-amber-600 to-green-700',
    jewel_tones: 'from-emerald-500 to-purple-600',
    monochromatic_blues: 'from-blue-300 to-blue-700',
    earthy_reds_and_oranges: 'from-red-600 to-orange-700',
    neon_graffiti: 'from-lime-400 to-pink-500',
    autumn_leaves: 'from-yellow-600 to-red-600',
    deep_sea_blues: 'from-blue-700 to-indigo-800',
    grayscale: 'from-gray-400 to-gray-700',
    sepia: 'from-yellow-700 to-amber-800',
    primary_colors: 'from-red-500 to-blue-500',
    rainbow_spectrum: 'from-red-400 via-yellow-400 to-blue-400',
    metallics: 'from-gray-400 to-yellow-600',
    // Original styles
    photorealistic: 'from-blue-400 to-blue-600',
    pixel_art: 'from-green-400 to-green-600',
    pencil_sketch: 'from-gray-300 to-gray-500',
    cyberpunk: 'from-purple-500 to-pink-500',
    impressionist: 'from-yellow-300 to-orange-400',
    abstract: 'from-red-400 to-purple-500',
    pop_art: 'from-cyan-400 to-pink-400',
    isometric: 'from-teal-400 to-blue-500',
    ukiyo_e: 'from-red-500 to-pink-600',
    low_poly: 'from-indigo-400 to-purple-600',
  };

  return gradientMap[styleOption] || 'from-gray-400 to-gray-600';
}

/**
 * Get the icon emoji for a style
 */
export function getStyleIcon(styleOption: string): string {
  const iconMap: Record<string, string> = {
    none: '🚫',
    // Medium styles
    stencil: '🎯',
    watercolor: '🎨',
    papercraft: '📄',
    marker_illustration: '🖊️',
    risograph: '🖨️',
    graffiti: '🎨',
    ink_wash: '🖋️',
    quilling: '🌀',
    charcoal: '⚫',
    oil_painting: '🖼️',
    collage: '📰',
    mosaic: '🧩',
    // Material styles
    porcelain: '🏺',
    light: '💡',
    candy: '🍭',
    bubbles: '🫧',
    crystals: '💎',
    ceramic: '🏺',
    plastic: '🧱',
    wood: '🪵',
    metal: '⚙️',
    water: '💧',
    glass: '🔍',
    sand: '🏖️',
    rain: '🌧️',
    // Photography styles
    high_key_photograph: '☀️',
    low_key_photograph: '🌙',
    low_angle_photograph: '📐',
    high_angle_photograph: '🔺',
    extreme_close_up: '🔍',
    low_shutter_speed_photograph: '💫',
    bokeh_photograph: '✨',
    silhouette_photograph: '👤',
    studio_lighting: '💡',
    black_and_white_photograph: '⚫',
    birds_eye_view: '🦅',
    worms_eye_view: '🪱',
    dutch_angle: '📐',
    long_exposure_photograph: '🌟',
    // Lighting styles
    natural_lighting: '☀️',
    light_and_shadow: '🌗',
    volumetric_lighting: '🌫️',
    neon_lighting: '🌈',
    golden_hour: '🌅',
    blue_hour: '🌆',
    backlighting: '💡',
    chiaroscuro: '🎭',
    god_rays: '☀️',
    candlelight: '🕯️',
    street_lighting: '🏮',
    softbox_lighting: '📦',
    moonlight: '🌙',
    fairy_lights: '✨',
    // Color and palette styles
    cool_tones: '❄️',
    warm_tones: '🔥',
    pastels: '🌸',
    vibrant: '🌈',
    earth_tones: '🌍',
    jewel_tones: '💎',
    monochromatic_blues: '🔵',
    earthy_reds_and_oranges: '🍂',
    neon_graffiti: '🌈',
    autumn_leaves: '🍁',
    deep_sea_blues: '🌊',
    grayscale: '⚫',
    sepia: '🟤',
    primary_colors: '🔴',
    rainbow_spectrum: '🌈',
    metallics: '⚙️',
    // Original styles
    photorealistic: '📷',
    pixel_art: '🎮',
    pencil_sketch: '✏️',
    cyberpunk: '🤖',
    impressionist: '🌅',
    abstract: '🎭',
    pop_art: '💥',
    isometric: '📐',
    ukiyo_e: '🌸',
    low_poly: '💎',
  };

  return iconMap[styleOption] || '🎨';
}
