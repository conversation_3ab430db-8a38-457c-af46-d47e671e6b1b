import lang from "./lang";

const {
  manageIdea,
} = lang;

export const mobileWidthLimit = 480;
export const tabletWidthLimit = 768;
export const lowResDeskLimit = 1024;
export const highResDeskLimit = 1280;

export const FILE_SIZE_10_MB = 10000000;
export const acceptedImageMimeTypes = [
  "image/jpeg",
  "image/png",
  "image/svg+xml",
  "image/webp",
  "image/gif",
  "image/apng",
  "image/avif",
];
export const agentLoadingStates = manageIdea.promptLoadingStates;

export const emailRegex =
  /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const usernameRegex = /^[A-Za-z0-9_]{4,15}$/;

export const SupabaseTables = {
  Projects: process.env.NEXT_PUBLIC_TABLE_MEDIA_PROJECTS || '',
};

// Platform character limits
export const PLATFORM_CHARACTER_LIMITS = {
  TWITTER: 280,
  TWITTER_PREMIUM: 25000,
  LINKEDIN: 3000,
  INSTAGRAM: 2200,
  FACEBOOK: 63206,
  YOUTUBE: 5000,
} as const;

export const PLATFORM_CANVAS_SIZES = {
  twitter: { 
    width: 1080, 
    height: 1080,
  },
  x: { 
    width: 1080, 
    height: 1080,
  },
  instagram: { 
    width: 1080, 
    height: 1350,
  },
  linkedin: { 
    width: 1200, 
    height: 1200,
  },
  facebook: { 
    width: 1200, 
    height: 628,
  },
  youtube: { 
    width: 1280, 
    height: 720,
  },
  default: { 
    width: 1200, 
    height: 1200,
  },
} as const;

export const tones = [
  "Professional",
  "Gen-Z",
  "Casual",
  "Academic",
  "Mentor",
  "Creative",
];

export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const isProd = process.env.NEXT_PUBLIC_ROOT_DOMAIN === "mediapilot.app";

export const ImageStyles = [
  {
    option: "none",
    label: "No Style",
  },
  // Medium styles
  {
    option: "stencil",
    label: "Stencil",
  },
  {
    option: "watercolor",
    label: "Watercolor",
  },
  {
    option: "papercraft",
    label: "Papercraft",
  },
  {
    option: "marker_illustration",
    label: "Marker Illustration",
  },
  {
    option: "risograph",
    label: "Risograph",
  },
  {
    option: "graffiti",
    label: "Graffiti",
  },
  {
    option: "ink_wash",
    label: "Ink Wash",
  },
  {
    option: "quilling",
    label: "Quilling",
  },
  {
    option: "charcoal",
    label: "Charcoal",
  },
  {
    option: "oil_painting",
    label: "Oil Painting",
  },
  {
    option: "collage",
    label: "Collage",
  },
  // Photography styles
  {
    option: "high_key_photograph",
    label: "High Key Photo",
  },
  {
    option: "low_key_photograph",
    label: "Low Key Photo",
  },
  {
    option: "low_angle_photograph",
    label: "Low Angle Photo",
  },
  {
    option: "high_angle_photograph",
    label: "High Angle Photo",
  },
  {
    option: "extreme_close_up",
    label: "Extreme Close-up",
  },
  {
    option: "low_shutter_speed_photograph",
    label: "Low Shutter Speed",
  },
  {
    option: "bokeh_photograph",
    label: "Bokeh Photo",
  },
  {
    option: "silhouette_photograph",
    label: "Silhouette Photo",
  },
  {
    option: "studio_lighting",
    label: "Studio Lighting",
  },
  {
    option: "black_and_white_photograph",
    label: "Black & White Photo",
  },
  {
    option: "birds_eye_view",
    label: "Bird's-eye View",
  },
  {
    option: "worms_eye_view",
    label: "Worm's-eye View",
  },
  {
    option: "dutch_angle",
    label: "Dutch Angle",
  },
  {
    option: "long_exposure_photograph",
    label: "Long Exposure",
  },
  // Lighting styles
  {
    option: "natural_lighting",
    label: "Natural Lighting",
  },
  {
    option: "light_and_shadow",
    label: "Light and Shadow",
  },
  {
    option: "volumetric_lighting",
    label: "Volumetric Lighting",
  },
  {
    option: "neon_lighting",
    label: "Neon Lighting",
  },
  {
    option: "golden_hour",
    label: "Golden Hour",
  },
  {
    option: "blue_hour",
    label: "Blue Hour",
  },
  {
    option: "backlighting",
    label: "Backlighting",
  },
  {
    option: "chiaroscuro",
    label: "Chiaroscuro",
  },
  {
    option: "god_rays",
    label: "God Rays",
  },
  {
    option: "candlelight",
    label: "Candlelight",
  },
  {
    option: "street_lighting",
    label: "Street Lighting",
  },
  {
    option: "softbox_lighting",
    label: "Softbox Lighting",
  },
  {
    option: "moonlight",
    label: "Moonlight",
  },
  {
    option: "fairy_lights",
    label: "Fairy Lights",
  },
  // Color and palette styles
  {
    option: "cool_tones",
    label: "Cool Tones",
  },
  {
    option: "warm_tones",
    label: "Warm Tones",
  },
  {
    option: "pastels",
    label: "Pastels",
  },
  {
    option: "vibrant",
    label: "Vibrant",
  },
  {
    option: "earth_tones",
    label: "Earth Tones",
  },
  {
    option: "jewel_tones",
    label: "Jewel Tones",
  },
  {
    option: "monochromatic_blues",
    label: "Monochromatic Blues",
  },
  {
    option: "earthy_reds_and_oranges",
    label: "Earthy Reds & Oranges",
  },
  {
    option: "neon_graffiti",
    label: "Neon Graffiti",
  },
  {
    option: "autumn_leaves",
    label: "Autumn Leaves",
  },
  {
    option: "deep_sea_blues",
    label: "Deep Sea Blues",
  },
  {
    option: "grayscale",
    label: "Grayscale",
  },
  {
    option: "sepia",
    label: "Sepia",
  },
  {
    option: "primary_colors",
    label: "Primary Colors",
  },
  {
    option: "rainbow_spectrum",
    label: "Rainbow Spectrum",
  },
  {
    option: "metallics",
    label: "Metallics",
  },
  // Original styles to maintain compatibility
  {
    option: "photorealistic",
    label: "Photorealistic",
  },
  {
    option: "pixel_art",
    label: "Pixel Art",
  },
  {
    option: "pencil_sketch",
    label: "Pencil Sketch",
  },
  {
    option: "cyberpunk",
    label: "Cyberpunk",
  },
  {
    option: "impressionist",
    label: "Impressionist",
  },
  {
    option: "abstract",
    label: "Abstract",
  },
  {
    option: "pop_art",
    label: "Pop Art",
  },
  {
    option: "isometric",
    label: "Isometric",
  },
  {
    option: "ukiyo_e",
    label: "Ukiyo-e",
  },
  {
    option: "low_poly",
    label: "Low Poly",
  },
];

export const tonesSelection = [
  {
    title: "👨 Professional",
    value: "Professional",
  },
  {
    title: "🧑‍💻 Gen-Z",
    value: "Gen-Z",
  },
  {
    title: "🤙 Casual",
    value: "Casual",
  },
  {
    title: "👨‍🏫 Academic",
    value: "Academic",
  },
  {
    title: "🧑‍🏫 Mentor",
    value: "Mentor",
  },
  {
    title: "👨‍🎨 Creative",
    value: "Creative",
  },
];

export const basicPlanLink = 'https://buy.stripe.com/test_fZeg342659J6cqQ3cc'

export const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'text/markdown',
  'text/plain',
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024;
